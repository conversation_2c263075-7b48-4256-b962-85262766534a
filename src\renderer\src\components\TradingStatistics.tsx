/**
 * Trading Statistics Component
 * Displays key trading metrics in a compact horizontal layout
 */

import React, { useCallback } from 'react'
import type { TradingStatisticsProps } from '../../../shared/types/trading'
import { STATISTICS_LABELS, CSS_CLASSES } from '../../../shared/constants/trading'

/**
 * Formats a number as currency with appropriate symbol
 * @param value - The numeric value to format
 * @param symbol - The currency symbol to use (default: '$')
 * @returns Formatted currency string
 */
const formatCurrency = (value: number, symbol: string = '$'): string => {
  if (!Number.isFinite(value)) {
    return `${symbol}0.00`
  }

  const isNegative = value < 0
  const absValue = Math.abs(value)
  const formatted = absValue.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
  return `${isNegative ? '-' : ''}${symbol}${formatted}`
}

/**
 * Formats win rate as percentage
 * @param value - The percentage value to format (0-100)
 * @returns Formatted percentage string
 */
const formatPercentage = (value: number): string => {
  if (!Number.isFinite(value)) {
    return '0.0%'
  }
  return `${Math.max(0, Math.min(100, value)).toFixed(1)}%`
}

/**
 * Formats trade count as integer with locale formatting
 * @param value - The trade count to format
 * @returns Formatted trade count string
 */
const formatTradeCount = (value: number): string => {
  if (!Number.isFinite(value) || value < 0) {
    return '0'
  }
  return Math.floor(value).toLocaleString('en-US')
}

/**
 * TradingStatistics Component
 * Renders a compact display of key trading metrics
 */
export const TradingStatistics: React.FC<TradingStatisticsProps> = ({
  statistics,
  showCurrency = true,
  currencySymbol = '$',
  compact = true, // Reserved for future layout variations
  ariaLabel
}) => {
  // Suppress unused variable warning for compact prop (reserved for future use)
  void compact
  /**
   * Determines the color class for P&L based on value
   */
  const getProfitLossColorClass = useCallback((value: number): string => {
    if (value > 0) return 'text-green-400'
    if (value < 0) return 'text-red-400'
    return 'text-gray-300'
  }, [])

  /**
   * Determines the color class for win rate based on value
   */
  const getWinRateColorClass = useCallback((value: number): string => {
    if (value >= 70) return 'text-green-400'
    if (value >= 50) return 'text-yellow-400'
    return 'text-red-400'
  }, [])

  return (
    <div
      className={`${CSS_CLASSES.statisticsContainer} flex items-center justify-between gap-4 p-1 bg-gray-800 border border-gray-700 rounded`}
      role="region"
      aria-label={ariaLabel || 'Trading statistics display'}
      aria-live="polite"
      aria-atomic="false"
    >
      {/* Balance - Most Important */}
      <div className={`${CSS_CLASSES.statisticsItem} flex flex-col items-center min-w-0`}>
        <span className={`${CSS_CLASSES.statisticsLabel} text-xs text-gray-400 font-medium`}>
          {STATISTICS_LABELS.balance}
        </span>
        <span className={`${CSS_CLASSES.statisticsValue} text-sm font-bold text-white truncate`}>
          {showCurrency
            ? formatCurrency(statistics.balance, currencySymbol)
            : statistics.balance.toFixed(2)}
        </span>
      </div>

      {/* P&L - Second Most Important */}
      <div className={`${CSS_CLASSES.statisticsItem} flex flex-col items-center min-w-0`}>
        <span className={`${CSS_CLASSES.statisticsLabel} text-xs text-gray-400 font-medium`}>
          {STATISTICS_LABELS.profitLoss}
        </span>
        <span
          className={`${CSS_CLASSES.statisticsValue} text-sm font-bold truncate ${getProfitLossColorClass(statistics.profitLoss)}`}
        >
          {showCurrency
            ? formatCurrency(statistics.profitLoss, currencySymbol)
            : statistics.profitLoss.toFixed(2)}
        </span>
      </div>

      {/* Win Rate - Third Most Important */}
      <div className={`${CSS_CLASSES.statisticsItem} flex flex-col items-center min-w-0`}>
        <span className={`${CSS_CLASSES.statisticsLabel} text-xs text-gray-400 font-medium`}>
          {STATISTICS_LABELS.winRate}
        </span>
        <span
          className={`${CSS_CLASSES.statisticsValue} text-sm font-bold truncate ${getWinRateColorClass(statistics.winRate)}`}
        >
          {formatPercentage(statistics.winRate)}
        </span>
      </div>

      {/* Total Trades - Fourth Most Important */}
      <div className={`${CSS_CLASSES.statisticsItem} flex flex-col items-center min-w-0`}>
        <span className={`${CSS_CLASSES.statisticsLabel} text-xs text-gray-400 font-medium`}>
          {STATISTICS_LABELS.totalTrades}
        </span>
        <span className={`${CSS_CLASSES.statisticsValue} text-sm font-bold text-white truncate`}>
          {formatTradeCount(statistics.totalTrades)}
        </span>
      </div>

      {/* Capital Pool Remaining - Fifth Most Important */}
      <div className={`${CSS_CLASSES.statisticsItem} flex flex-col items-center min-w-0`}>
        <span className={`${CSS_CLASSES.statisticsLabel} text-xs text-gray-400 font-medium`}>
          {STATISTICS_LABELS.capitalPoolRemaining}
        </span>
        <span className={`${CSS_CLASSES.statisticsValue} text-sm font-bold text-white truncate`}>
          {showCurrency
            ? formatCurrency(statistics.capitalPoolRemaining, currencySymbol)
            : statistics.capitalPoolRemaining.toFixed(2)}
        </span>
      </div>
    </div>
  )
}

export default TradingStatistics
