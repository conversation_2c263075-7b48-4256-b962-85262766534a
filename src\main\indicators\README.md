# Technical Indicators System

A comprehensive, high-performance technical indicators library designed for real-time trading applications with TypeScript support, streaming capabilities, and O(n) optimized calculations.

## Features

- **High Performance**: O(n) complexity algorithms with rolling window optimization
- **Real-time Streaming**: Live data updates with event-driven architecture
- **Type Safety**: Full TypeScript support with comprehensive type definitions
- **Memory Efficient**: Automatic memory management and configurable history limits
- **Extensible**: Plugin-based architecture for custom indicators
- **Error Handling**: Comprehensive validation and error recovery
- **Event System**: Real-time notifications for data updates and calculations

## Quick Start

### Basic Usage

```typescript
import { SimpleMovingAverage, createSMA } from './indicators'

// Create SMA with default configuration (period: 20)
const sma = createSMA({ period: 20 })

// Add data points
const dataPoints = [
  { value: 100, timestamp: Date.now() },
  { value: 102, timestamp: Date.now() + 1000 },
  { value: 98, timestamp: Date.now() + 2000 }
]

// Add data and get results
dataPoints.forEach((point) => {
  const result = sma.addData(point)
  if (result) {
    console.log(`SMA: ${result.value}`)
  }
})
```

### Advanced Configuration

```typescript
import { SimpleMovingAverage } from './indicators'

const sma = new SimpleMovingAverage({
  period: 50,
  enableStreaming: true,
  maxHistorySize: 1000,
  priceType: 'typical', // (high + low + close) / 3
  useSmoothing: true,
  smoothingFactor: 0.1
})

// Set up streaming callbacks
sma.onStreamUpdate((update) => {
  console.log('New SMA value:', update.indicatorValue.value)
})

// Event listeners
sma.on('value-calculated', (event) => {
  console.log('SMA calculated:', event.payload)
})
```

### Batch Processing

```typescript
import { IndicatorUtils } from './indicators'

// Convert price array to data points
const prices = [100, 102, 98, 105, 103, 99, 101]
const dataPoints = IndicatorUtils.pricesToDataPoints(prices)

// Process in batch
const sma = createSMA({ period: 5 })
const results = sma.addDataBatch(dataPoints)

// Extract values
const values = IndicatorUtils.extractValues(results)
console.log('SMA values:', values)
```

## API Reference

### SimpleMovingAverage Class

#### Constructor

```typescript
new SimpleMovingAverage(config?: Partial<SMAConfig>)
```

**Parameters:**

- `config.period` (number): Period for SMA calculation (default: 20)
- `config.enableStreaming` (boolean): Enable real-time streaming (default: true)
- `config.maxHistorySize` (number): Maximum data points to keep (default: 1000)
- `config.priceType` (string): Price type to use ('close', 'open', 'high', 'low', 'typical', 'weighted')
- `config.useSmoothing` (boolean): Apply exponential smoothing (default: false)
- `config.smoothingFactor` (number): Smoothing factor 0-1 (default: 0.1)

#### Methods

##### addData(dataPoint)

Add a single data point and calculate SMA value.

```typescript
const result = sma.addData({ value: 100, timestamp: Date.now() })
if (result) {
  console.log(`SMA: ${result.value} at ${result.timestamp}`)
}
```

##### addDataBatch(dataPoints)

Add multiple data points in batch for better performance.

```typescript
const results = sma.addDataBatch([{ value: 100 }, { value: 102 }, { value: 98 }])
```

##### calculate()

Recalculate SMA for all current data points.

```typescript
const result = sma.calculate()
console.log(`Calculated ${result.values.length} SMA values`)
console.log(`Performance: ${result.metadata.performance?.calculationTime}ms`)
```

##### getCurrentAverage()

Get current SMA value without adding new data.

```typescript
const currentSMA = sma.getCurrentAverage()
if (currentSMA !== null) {
  console.log(`Current SMA: ${currentSMA}`)
}
```

##### reset()

Reset indicator to initial state.

```typescript
sma.reset()
console.log(`Data count after reset: ${sma.dataCount}`) // 0
```

#### Properties

- `name` (string): Indicator name
- `config` (SMAConfig): Current configuration
- `isReady` (boolean): Whether indicator has enough data
- `dataCount` (number): Number of data points
- `latestValue` (IndicatorOutputPoint | null): Latest calculated value

#### Events

```typescript
// Data added
sma.on('data-added', (event) => {
  console.log('Data added:', event.payload)
})

// Value calculated
sma.on('value-calculated', (event) => {
  console.log('New value:', event.payload.outputValue)
})

// Error occurred
sma.on('error-occurred', (event) => {
  console.error('Indicator error:', event.payload.error)
})

// Configuration changed
sma.on('config-changed', (event) => {
  console.log('Config updated:', event.payload.newConfig)
})
```

### Factory Functions

#### createSMA(config)

Factory function to create SMA indicator.

```typescript
const sma = createSMA({ period: 30 })
```

#### calculateSMA(prices, period) [Deprecated]

Legacy function for backward compatibility.

```typescript
const values = calculateSMA([100, 102, 98, 105], 3)
// Returns: [100, 101.67, 103]
```

### Utility Functions

#### IndicatorUtils.validateConfig(config)

Validate indicator configuration.

```typescript
const validation = IndicatorUtils.validateConfig({ period: 20 })
if (!validation.isValid) {
  console.error('Config errors:', validation.errors)
}
```

#### IndicatorUtils.pricesToDataPoints(prices, startTimestamp?)

Convert price array to data points.

```typescript
const dataPoints = IndicatorUtils.pricesToDataPoints([100, 102, 98])
```

#### IndicatorUtils.extractValues(outputPoints)

Extract values from output points.

```typescript
const values = IndicatorUtils.extractValues(sma.getValues())
```

### Indicator Registry

```typescript
import { indicatorRegistry } from './indicators'

// Create indicator by name
const sma = indicatorRegistry.create('sma', { period: 20 })

// Check available indicators
const available = indicatorRegistry.getAvailableIndicators()
console.log('Available indicators:', available)

// Register custom indicator
indicatorRegistry.register('custom', (config) => new CustomIndicator(config))
```

## Performance Considerations

### Memory Management

- Automatic cleanup when `maxHistorySize` is exceeded
- Configurable memory limits and garbage collection
- Circular buffer optimization for large datasets

### Calculation Optimization

- O(n) complexity using rolling window technique
- Incremental calculations for real-time updates
- Batch processing for large datasets

### Best Practices

1. **Use appropriate period sizes**: Larger periods require more memory
2. **Enable streaming for real-time applications**: Better performance than polling
3. **Set reasonable history limits**: Prevent memory leaks in long-running applications
4. **Use batch processing**: More efficient for historical data analysis
5. **Monitor performance metrics**: Available in calculation results

## Error Handling

The indicator system provides comprehensive error handling:

```typescript
try {
  const sma = createSMA({ period: -1 }) // Invalid period
} catch (error) {
  console.error('Configuration error:', error.message)
}

// Validation before adding data
const validation = sma.validateInput([{ value: NaN }])
if (!validation.isValid) {
  console.error('Invalid data:', validation.errors)
}
```

## Integration with Trading Bot

The indicator system is designed to integrate seamlessly with the trading bot:

```typescript
import { SimpleMovingAverage } from '../indicators'

class TradingStrategy {
  private sma20: SimpleMovingAverage
  private sma50: SimpleMovingAverage

  constructor() {
    this.sma20 = createSMA({ period: 20, enableStreaming: true })
    this.sma50 = createSMA({ period: 50, enableStreaming: true })

    // Set up crossover detection
    this.sma20.on('value-calculated', () => this.checkCrossover())
  }

  private checkCrossover() {
    const sma20Value = this.sma20.getCurrentAverage()
    const sma50Value = this.sma50.getCurrentAverage()

    if (sma20Value && sma50Value) {
      if (sma20Value > sma50Value) {
        console.log('Golden cross detected - potential buy signal')
      }
    }
  }

  public addPriceData(price: number) {
    const dataPoint = { value: price, timestamp: Date.now() }
    this.sma20.addData(dataPoint)
    this.sma50.addData(dataPoint)
  }
}
```

## Type Definitions

All types are fully documented and exported for use in your applications:

```typescript
import type {
  BaseIndicator,
  SMAIndicator,
  IndicatorDataPoint,
  IndicatorOutputPoint,
  IndicatorResult,
  SMAConfig
} from './indicators'
```

## Contributing

When adding new indicators:

1. Extend `BaseIndicatorClass`
2. Implement required abstract methods
3. Add comprehensive JSDoc documentation
4. Include unit tests
5. Register in the indicator registry
6. Update this documentation
