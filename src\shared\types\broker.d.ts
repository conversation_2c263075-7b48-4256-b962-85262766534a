/**
 * TypeScript type definitions for PocketOption broker
 * Contains all custom types used by the PocketOption class
 */

/**
 * Interface for authentication data extracted from session ID
 */
interface AuthData {
  session: string
  uid: number
}

/**
 * Interface for socket authentication payload
 */
interface SocketAuthPayload {
  isDemo: number
  isFastHistory: boolean
  platform: number
  session: string
  uid: number
}

/**
 * Interface for heartbeat configuration
 */
interface HeartbeatConfig {
  interval: number
  timeout: number
  maxMissedBeats: number
}

/**
 * Type for heartbeat statistics returned by getHeartbeatStats method
 */
interface HeartbeatStats {
  health: string // Using string instead of enum to avoid circular dependency
  missedBeats: number
  lastSent: number
  lastReceived: number
  isActive: boolean
}

/**
 * Type for connection state change event data
 */
interface ConnectionStateChangeData {
  from: string
  to: string
  timestamp: number
}

/**
 * Type for heartbeat health change event data
 */
interface HeartbeatHealthChangeData {
  from: string
  to: string
  missedBeats: number
}

/**
 * Type for heartbeat sent event data
 */
interface HeartbeatSentData {
  timestamp: number
  missedBeats: number
}

/**
 * Type for heartbeat received event data
 */
interface HeartbeatReceivedData {
  timestamp: number
  responseTime: number
  missedBeats: number
}

/**
 * Type for heartbeat failed event data
 */
interface HeartbeatFailedData {
  missedBeats: number
  maxMissedBeats: number
}

/**
 * Type for disconnect event data
 */
interface DisconnectEventData {
  reason: string
}

/**
 * Type for error event data
 */
interface ErrorEventData {
  error: string
}
