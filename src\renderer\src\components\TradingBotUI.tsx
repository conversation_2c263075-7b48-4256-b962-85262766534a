/**
 * Trading Bot UI Component
 * Main component for the trading bot interface with two-column layout
 */

import React, { useState, useCallback, useEffect, useRef } from 'react'
import type {
  TradingBotUIProps,
  TradingBotUIState,
  TradingBotConfig,
  TradingBotFormData,
  TradingBotFormErrors,
  TradeExpiryDuration
} from '../../../shared/types/trading'
import { useTradingContext } from '../hooks/useTradingContext'
import {
  DEFAULT_TRADING_CONFIG,
  VALIDATION_CONSTRAINTS,
  ERROR_MESSAGES,
  CSS_CLASSES
} from '../../../shared/constants/trading'
import { NumericInput } from './NumericInput'
import { TradeExpiryDurationSelector } from './TradeExpiryDurationSelector'
import { StartStopButton } from './StartStopButton'
import { TradingStatistics as TradingStatisticsComponent } from './TradingStatistics'

/**
 * Main Trading Bot UI Component
 */
export const TradingBotUI: React.FC<TradingBotUIProps> = ({
  initialConfig,
  onStart,
  onStop,
  onConfigChange
}) => {
  // Get trading context
  const { tradingStats, setTradingStats, resetTradingStats } = useTradingContext()

  // Initialize form data with default or provided initial config
  const [uiState, setUIState] = useState<TradingBotUIState>(() => {
    const config = { ...DEFAULT_TRADING_CONFIG, ...initialConfig }
    return {
      state: 'stopped',
      formData: {
        tradeCapital: config.tradeCapital.toString(),
        targetProfit: config.targetProfit.toString(),
        tradeAmount: config.tradeAmount.toString(),
        tradeExpiryDuration: config.tradeExpiryDuration
      },
      errors: {},
      isValid: true
    }
  })

  // Ref to store current trade amount for simulation
  const tradeAmountRef = useRef<number>(
    parseFloat(uiState.formData.tradeAmount) || DEFAULT_TRADING_CONFIG.tradeAmount
  )

  /**
   * Validates a numeric field
   */
  const validateNumericField = useCallback(
    (
      value: string,
      fieldName: keyof typeof VALIDATION_CONSTRAINTS,
      tradeCapital?: number
    ): string | undefined => {
      if (!value.trim()) {
        return ERROR_MESSAGES[fieldName].required
      }

      const numValue = parseFloat(value)
      if (isNaN(numValue)) {
        return ERROR_MESSAGES[fieldName].invalid
      }

      const constraints = VALIDATION_CONSTRAINTS[fieldName]
      if (numValue < constraints.min) {
        return ERROR_MESSAGES[fieldName].tooLow
      }
      if (numValue > constraints.max) {
        return ERROR_MESSAGES[fieldName].tooHigh
      }

      // Special validation for trade amount vs trade capital
      if (fieldName === 'tradeAmount' && tradeCapital && numValue > tradeCapital) {
        return ERROR_MESSAGES.tradeAmount.exceedsCapital
      }

      return undefined
    },
    []
  )

  /**
   * Validates the entire form
   */
  const validateForm = useCallback(
    (formData: TradingBotFormData): TradingBotFormErrors => {
      const errors: TradingBotFormErrors = {}
      const tradeCapital = parseFloat(formData.tradeCapital)

      // Validate numeric fields
      errors.tradeCapital = validateNumericField(formData.tradeCapital, 'tradeCapital')
      errors.targetProfit = validateNumericField(formData.targetProfit, 'targetProfit')
      errors.tradeAmount = validateNumericField(
        formData.tradeAmount,
        'tradeAmount',
        isNaN(tradeCapital) ? undefined : tradeCapital
      )

      // Validate trade expiry duration
      if (!formData.tradeExpiryDuration) {
        errors.tradeExpiryDuration = ERROR_MESSAGES.tradeExpiryDuration.required
      }

      return errors
    },
    [validateNumericField]
  )

  /**
   * Updates form data and validates
   */
  const updateFormData = useCallback(
    (updates: Partial<TradingBotFormData>) => {
      setUIState((prevState) => {
        const newFormData = { ...prevState.formData, ...updates }
        const errors = validateForm(newFormData)
        const isValid = Object.values(errors).every((error) => !error)

        const newState = {
          ...prevState,
          formData: newFormData,
          errors,
          isValid
        }

        // Notify parent of config changes
        if (onConfigChange && isValid) {
          const config: Partial<TradingBotConfig> = {
            tradeCapital: parseFloat(newFormData.tradeCapital),
            targetProfit: parseFloat(newFormData.targetProfit),
            tradeAmount: parseFloat(newFormData.tradeAmount),
            tradeExpiryDuration: newFormData.tradeExpiryDuration || undefined
          }
          onConfigChange(config)
        }

        return newState
      })
    },
    [validateForm, onConfigChange]
  )

  /**
   * Handles numeric input changes
   */
  const handleNumericInputChange = useCallback(
    (field: keyof TradingBotFormData) => (value: string) => {
      updateFormData({ [field]: value })
    },
    [updateFormData]
  )

  /**
   * Handles trade expiry duration change
   */
  const handleTradeExpiryDurationChange = useCallback(
    (duration: TradeExpiryDuration) => {
      updateFormData({ tradeExpiryDuration: duration })
    },
    [updateFormData]
  )

  /**
   * Update trade amount ref when form data changes
   */
  useEffect(() => {
    tradeAmountRef.current =
      parseFloat(uiState.formData.tradeAmount) || DEFAULT_TRADING_CONFIG.tradeAmount
  }, [uiState.formData.tradeAmount])

  /**
   * Update trading statistics when trade capital changes
   */
  useEffect(() => {
    const newCapital = parseFloat(uiState.formData.tradeCapital) || 0
    setTradingStats((prev) => ({
      ...prev,
      balance: newCapital,
      capitalPoolRemaining:
        newCapital - prev.totalTrades * parseFloat(uiState.formData.tradeAmount || '0')
    }))
  }, [uiState.formData.tradeCapital, uiState.formData.tradeAmount, setTradingStats])

  /**
   * Simulate trading activity when bot is running (for demonstration)
   */
  useEffect(() => {
    if (uiState.state !== 'running') return

    const interval = setInterval(() => {
      setTradingStats((prev) => {
        try {
          // Simulate a trade with 60% win rate
          const isWin = Math.random() < 0.6
          const tradeAmount = tradeAmountRef.current
          const payout = isWin ? tradeAmount * 0.8 : -tradeAmount // 80% payout on win

          const newTotalTrades = prev.totalTrades + 1
          const newProfitLoss = prev.profitLoss + payout
          const newBalance = prev.balance + payout
          const newWinRate =
            newTotalTrades > 0
              ? (prev.winRate * prev.totalTrades + (isWin ? 100 : 0)) / newTotalTrades
              : 0
          const newCapitalRemaining = Math.max(0, newBalance - newTotalTrades * tradeAmount)

          return {
            balance: Number.isFinite(newBalance) ? newBalance : prev.balance,
            profitLoss: Number.isFinite(newProfitLoss) ? newProfitLoss : prev.profitLoss,
            winRate: Number.isFinite(newWinRate) ? newWinRate : prev.winRate,
            totalTrades: newTotalTrades,
            capitalPoolRemaining: Number.isFinite(newCapitalRemaining)
              ? newCapitalRemaining
              : prev.capitalPoolRemaining
          }
        } catch (error) {
          console.error('Error in trading simulation:', error)
          return prev // Return previous state on error
        }
      })
    }, 3000) // Simulate a trade every 3 seconds

    return () => clearInterval(interval)
  }, [uiState.state, setTradingStats])

  /**
   * Handles start/stop button click
   */
  const handleStartStopClick = useCallback(() => {
    if (uiState.state === 'stopped') {
      if (uiState.isValid) {
        const config: TradingBotConfig = {
          tradeCapital: parseFloat(uiState.formData.tradeCapital),
          targetProfit: parseFloat(uiState.formData.targetProfit),
          tradeAmount: parseFloat(uiState.formData.tradeAmount),
          tradeExpiryDuration: uiState.formData.tradeExpiryDuration!
        }

        // Reset statistics when starting
        const initialCapital = parseFloat(uiState.formData.tradeCapital)
        resetTradingStats(initialCapital)

        setUIState((prev) => ({ ...prev, state: 'running' }))
        onStart?.(config)
      }
    } else {
      setUIState((prev) => ({ ...prev, state: 'stopped' }))
      onStop?.()
    }
  }, [uiState.state, uiState.isValid, uiState.formData, onStart, onStop, resetTradingStats])

  // Validate form on mount
  useEffect(() => {
    const errors = validateForm(uiState.formData)
    const isValid = Object.values(errors).every((error) => !error)
    setUIState((prev) => ({ ...prev, errors, isValid }))
  }, [validateForm, uiState.formData])

  return (
    <div className={`${CSS_CLASSES.container} space-y-6 p-6 max-w-4xl mx-auto`}>
      {/* Trading Statistics Display */}
      <TradingStatisticsComponent
        statistics={tradingStats}
        showCurrency={true}
        currencySymbol="$"
        ariaLabel="Current trading session statistics"
      />

      {/* Main Trading Controls */}
      <div className="grid grid-cols-2 gap-8">
        {/* Left Column - Input Controls */}
        <div className={`${CSS_CLASSES.leftColumn} space-y-6`}>
          <NumericInput
            label="Trade Capital"
            value={uiState.formData.tradeCapital}
            onChange={handleNumericInputChange('tradeCapital')}
            placeholder="Enter total pot money"
            error={uiState.errors.tradeCapital}
            required
            min={VALIDATION_CONSTRAINTS.tradeCapital.min}
            max={VALIDATION_CONSTRAINTS.tradeCapital.max}
            step={VALIDATION_CONSTRAINTS.tradeCapital.step}
            ariaLabel="Trade capital amount in dollars"
          />

          <NumericInput
            label="Target Profit (Take Profit)"
            value={uiState.formData.targetProfit}
            onChange={handleNumericInputChange('targetProfit')}
            placeholder="Enter desired profit target"
            error={uiState.errors.targetProfit}
            required
            min={VALIDATION_CONSTRAINTS.targetProfit.min}
            max={VALIDATION_CONSTRAINTS.targetProfit.max}
            step={VALIDATION_CONSTRAINTS.targetProfit.step}
            ariaLabel="Target profit amount in dollars"
          />

          <NumericInput
            label="Trade Amount"
            value={uiState.formData.tradeAmount}
            onChange={handleNumericInputChange('tradeAmount')}
            placeholder="Enter amount per trade"
            error={uiState.errors.tradeAmount}
            required
            min={VALIDATION_CONSTRAINTS.tradeAmount.min}
            max={VALIDATION_CONSTRAINTS.tradeAmount.max}
            step={VALIDATION_CONSTRAINTS.tradeAmount.step}
            ariaLabel="Trade amount per individual trade in dollars"
          />

          <TradeExpiryDurationSelector
            selectedDuration={uiState.formData.tradeExpiryDuration}
            onChange={handleTradeExpiryDurationChange}
            error={uiState.errors.tradeExpiryDuration}
            ariaLabel="Select trade expiry duration"
          />
        </div>

        {/* Right Column - Start/Stop Button */}
        <div className={`${CSS_CLASSES.rightColumn} flex items-center justify-center`}>
          <StartStopButton
            state={uiState.state}
            onClick={handleStartStopClick}
            disabled={uiState.state === 'stopped' && !uiState.isValid}
            ariaLabel="Toggle trading bot state"
          />
        </div>
      </div>
    </div>
  )
}

export default TradingBotUI
