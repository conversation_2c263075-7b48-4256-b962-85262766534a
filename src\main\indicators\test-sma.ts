/**
 * Simple test file to verify SMA implementation
 * Run this to ensure the indicator system is working correctly
 */

import { SimpleMovingAverage, createSMA, calculateSMA, IndicatorUtils } from './index'
import { logger } from '../../shared/utils/logger'

/**
 * Test basic SMA calculation
 */
function testBasicSMA(): boolean {
  console.log('Testing basic SMA calculation...')

  try {
    const sma = createSMA({ period: 3 })
    const testPrices = [10, 20, 30, 40, 50]

    testPrices.forEach((price) => {
      sma.addData({ value: price, timestamp: Date.now() })
    })

    const values = IndicatorUtils.extractValues(sma.getValues())
    const expected = [20, 30, 40] // SMA(3) for [10,20,30], [20,30,40], [30,40,50]

    console.log('Calculated values:', values)
    console.log('Expected values:', expected)

    // Check if values are approximately correct (allowing for floating point precision)
    const isCorrect =
      values.length === expected.length &&
      values.every((val, i) => Math.abs(val - expected[i]) < 0.001)

    if (isCorrect) {
      console.log('✅ Basic SMA test passed')
      return true
    } else {
      console.log('❌ Basic SMA test failed')
      return false
    }
  } catch (error) {
    console.error('❌ Basic SMA test error:', error)
    return false
  }
}

/**
 * Test legacy function compatibility
 */
function testLegacyFunction(): boolean {
  console.log('\nTesting legacy function compatibility...')

  try {
    const prices = [10, 20, 30, 40, 50]
    const period = 3
    const values = calculateSMA(prices, period)
    const expected = [20, 30, 40]

    console.log('Legacy function values:', values)
    console.log('Expected values:', expected)

    const isCorrect =
      values.length === expected.length &&
      values.every((val, i) => Math.abs(val - expected[i]) < 0.001)

    if (isCorrect) {
      console.log('✅ Legacy function test passed')
      return true
    } else {
      console.log('❌ Legacy function test failed')
      return false
    }
  } catch (error) {
    console.error('❌ Legacy function test error:', error)
    return false
  }
}

/**
 * Test performance optimization
 */
function testPerformance(): boolean {
  console.log('\nTesting performance optimization...')

  try {
    const sma = createSMA({ period: 20 })
    const largeDataset = Array.from({ length: 1000 }, (_, i) => i + 1)

    const startTime = performance.now()

    // Add data points
    largeDataset.forEach((value) => {
      sma.addData({ value, timestamp: Date.now() })
    })

    const endTime = performance.now()
    const duration = endTime - startTime

    console.log(`Processed ${largeDataset.length} data points in ${duration.toFixed(2)}ms`)
    console.log(`Average time per calculation: ${(duration / largeDataset.length).toFixed(4)}ms`)

    // Performance should be reasonable (less than 1ms per calculation on average)
    const isPerformant = duration / largeDataset.length < 1

    if (isPerformant) {
      console.log('✅ Performance test passed')
      return true
    } else {
      console.log('⚠️  Performance test warning: calculations may be slower than expected')
      return true // Don't fail on performance, just warn
    }
  } catch (error) {
    console.error('❌ Performance test error:', error)
    return false
  }
}

/**
 * Test streaming functionality
 */
function testStreaming(): Promise<boolean> {
  console.log('\nTesting streaming functionality...')

  return new Promise((resolve) => {
    try {
      const sma = createSMA({ period: 3, enableStreaming: true })
      let streamUpdateCount = 0
      let eventCount = 0

      // Set up stream callback
      sma.onStreamUpdate((update) => {
        streamUpdateCount++
        console.log(`Stream update ${streamUpdateCount}: ${update.indicatorValue.value.toFixed(2)}`)
      })

      // Set up event listener
      sma.on('value-calculated', () => {
        eventCount++
      })

      // Add test data
      const testData = [10, 20, 30, 40]
      testData.forEach((value, index) => {
        setTimeout(() => {
          sma.addData({ value, timestamp: Date.now() })

          // Check results after last data point
          if (index === testData.length - 1) {
            setTimeout(() => {
              const success = streamUpdateCount > 0 && eventCount > 0
              if (success) {
                console.log(
                  `✅ Streaming test passed (${streamUpdateCount} stream updates, ${eventCount} events)`
                )
              } else {
                console.log('❌ Streaming test failed')
              }
              resolve(success)
            }, 100)
          }
        }, index * 50)
      })
    } catch (error) {
      console.error('❌ Streaming test error:', error)
      resolve(false)
    }
  })
}

/**
 * Test error handling
 */
function testErrorHandling(): boolean {
  console.log('\nTesting error handling...')

  try {
    let errorCaught = false

    // Test invalid configuration
    try {
      createSMA({ period: -1 })
    } catch (error) {
      errorCaught = true
      console.log('✅ Invalid configuration error caught correctly')
    }

    if (!errorCaught) {
      console.log('❌ Invalid configuration should have thrown an error')
      return false
    }

    // Test input validation
    const sma = createSMA({ period: 3 })
    const validation = sma.validateInput([{ value: NaN, timestamp: Date.now() }])

    if (!validation.isValid && validation.errors.length > 0) {
      console.log('✅ Input validation working correctly')
      return true
    } else {
      console.log('❌ Input validation failed to catch invalid data')
      return false
    }
  } catch (error) {
    console.error('❌ Error handling test error:', error)
    return false
  }
}

/**
 * Run all tests
 */
export async function runTests(): Promise<void> {
  console.log('🧪 Running SMA Implementation Tests...\n')

  const results: boolean[] = []

  // Run synchronous tests
  results.push(testBasicSMA())
  results.push(testLegacyFunction())
  results.push(testPerformance())
  results.push(testErrorHandling())

  // Run asynchronous streaming test
  const streamingResult = await testStreaming()
  results.push(streamingResult)

  // Summary
  const passed = results.filter((r) => r).length
  const total = results.length

  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`)

  if (passed === total) {
    console.log('🎉 All tests passed! SMA implementation is working correctly.')
    logger.info('SMA-Test', 'All tests passed successfully')
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.')
    logger.warn('SMA-Test', `${total - passed} tests failed`)
  }
}

/**
 * Quick verification function
 */
export function quickTest(): void {
  console.log('🚀 Quick SMA Test...')

  const sma = createSMA({ period: 5 })
  const prices = [100, 102, 98, 105, 103, 99, 101]

  console.log('Input prices:', prices)

  prices.forEach((price) => {
    const result = sma.addData({ value: price, timestamp: Date.now() })
    if (result) {
      console.log(`SMA: ${result.value.toFixed(2)}`)
    }
  })

  console.log('✅ Quick test completed')
}

// Export for external use
export default {
  runTests,
  quickTest,
  testBasicSMA,
  testLegacyFunction,
  testPerformance,
  testStreaming,
  testErrorHandling
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error)
}
